<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评审管理 - 可研报告评审助手</title>
    <link href="/static/bootstrap.min.css" rel="stylesheet">
    <link href="/static/all.min.css" rel="stylesheet">
    <style>
        .review-card {
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .review-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        .review-content {
            padding: 15px;
        }
        .status-badge {
            font-size: 0.8em;
        }
        .stats-info {
            font-size: 0.9em;
            color: #6c757d;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">可研报告评审助手</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">报告评审</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/topics">专题管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reports">报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/reviews">评审管理</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>评审管理</h1>
        </div>

        <!-- 过滤器 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <select class="form-select" id="topicFilter" onchange="filterReviews()">
                    <option value="">所有专题</option>
                </select>
            </div>
            <div class="col-md-4">
                <select class="form-select" id="reportFilter" onchange="filterReviews()">
                    <option value="">所有报告</option>
                </select>
            </div>
        </div>

        <!-- 评审报告选择 -->
        <div class="card mb-4" id="reviewSection">
            <div class="card-header">
                <h5 class="mb-0">开始新评审</h5>
            </div>
            <div class="card-body">
                <!-- 单个报告评审 -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="selectReport" class="form-label">选择单个报告进行评审</label>
                        <select class="form-select" id="selectReport">
                            <option value="">请选择报告</option>
                        </select>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="button" class="btn btn-success" onclick="startReview()">
                            <i class="fas fa-play"></i> 开始评审
                        </button>
                    </div>
                </div>


            </div>
        </div>

        <!-- 加载动画 -->
        <div class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">评审中...</span>
            </div>
            <p class="mt-2">正在评审报告，请稍候...</p>
        </div>

        <!-- 评审记录列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">评审记录</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-primary" onclick="selectAllReports()">
                        <i class="fas fa-check-square"></i> 全选
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAllReports()">
                        <i class="fas fa-square"></i> 清空
                    </button>
                    <button type="button" class="btn btn-sm btn-warning" onclick="startBatchReview()">
                        <i class="fas fa-tasks"></i> 批量评审
                    </button>
                    <button type="button" class="btn btn-sm btn-info" onclick="generateMultiReportSummary()">
                        <i class="fas fa-chart-line"></i> 汇总分析
                    </button>
                    <span id="selectedCount" class="ms-3 text-muted">已选择 0 个报告</span>
                </div>
            </div>
            <div class="card-body">
                <div id="reviewsList">
                    <!-- 评审记录将在这里动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 评审结果详情模态框 -->
    <div class="modal fade" id="reviewDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">评审结果详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="reviewDetailContent">
                        <!-- 评审详情内容将在这里显示 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let allReviews = [];
        let allReports = [];
        let allTopics = [];
        let selectedReports = new Set(); // 存储选中的报告ID

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查URL参数，如果有report_id则自动选择该报告
            const urlParams = new URLSearchParams(window.location.search);
            const reportId = urlParams.get('report_id');

            loadTopics();
            loadReports();
            loadReviews();

            if (reportId) {
                setTimeout(() => {
                    document.getElementById('selectReport').value = reportId;
                }, 1000);
            }
        });

        // 加载专题列表
        async function loadTopics() {
            try {
                const response = await fetch('/api/topics');
                const result = await response.json();

                if (result.success) {
                    allTopics = result.data;
                    populateTopicFilter();
                }
            } catch (error) {
                console.error('加载专题列表失败:', error);
            }
        }

        // 加载报告列表
        async function loadReports() {
            try {
                const response = await fetch('/api/reports');
                const result = await response.json();

                if (result.success) {
                    allReports = result.data;
                    populateReportSelects();
                }
            } catch (error) {
                console.error('加载报告列表失败:', error);
            }
        }

        // 加载评审记录
        async function loadReviews() {
            try {
                const response = await fetch('/api/reviews');
                const result = await response.json();

                if (result.success) {
                    allReviews = result.data;
                    displayReviews(allReviews);
                } else {
                    alert('加载评审记录失败: ' + result.error);
                }
            } catch (error) {
                alert('加载评审记录失败: ' + error.message);
            }
        }

        // 填充专题过滤器
        function populateTopicFilter() {
            const select = document.getElementById('topicFilter');
            select.innerHTML = '<option value="">所有专题</option>';

            allTopics.forEach(topic => {
                const option = document.createElement('option');
                option.value = topic.id;
                option.textContent = topic.name;
                select.appendChild(option);
            });
        }

        // 填充报告选择框
        function populateReportSelects() {
            const selects = ['reportFilter', 'selectReport'];

            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (selectId === 'reportFilter') {
                    select.innerHTML = '<option value="">所有报告</option>';
                } else {
                    select.innerHTML = '<option value="">请选择报告</option>';
                }

                allReports.forEach(report => {
                    const option = document.createElement('option');
                    option.value = report.id;
                    option.textContent = report.name;
                    select.appendChild(option);
                });
            });
        }

        // 过滤评审记录
        function filterReviews() {
            const reportId = document.getElementById('reportFilter').value;
            const topicId = document.getElementById('topicFilter').value;
            let filteredReviews = allReviews;

            if (reportId) {
                filteredReviews = filteredReviews.filter(review => review.report_id === reportId);
            }

            if (topicId) {
                filteredReviews = filteredReviews.filter(review => review.topic_id === topicId);
            }

            displayReviews(filteredReviews);
        }

        // 显示评审记录
        function displayReviews(reviews) {
            const container = document.getElementById('reviewsList');

            if (reviews.length === 0) {
                container.innerHTML = '<div class="text-center text-muted"><p>暂无评审记录。</p></div>';
                return;
            }

            container.innerHTML = reviews.map(review => {
                const report = allReports.find(r => r.id === review.report_id);
                const topic = allTopics.find(t => t.id === review.topic_id);
                const reportName = report ? report.name : '未知报告';
                const topicName = topic ? topic.name : '未分类';

                // 获取统计信息
                const stats = review.result?.statistics || {};
                const complianceRate = stats.compliance_rate || 0;
                const totalCriteria = stats.total_criteria || 0;

                const isSelected = selectedReports.has(review.report_id);

                return `
                    <div class="review-card">
                        <div class="review-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <input type="checkbox" class="form-check-input me-3"
                                           ${isSelected ? 'checked' : ''}
                                           onchange="toggleReportSelection('${review.report_id}')">
                                    <div>
                                        <h5 class="mb-1">${reportName}</h5>
                                        <span class="badge bg-secondary status-badge">${topicName}</span>
                                        <span class="badge bg-success status-badge ms-2">${review.status}</span>
                                    </div>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewReviewDetail('${review.id}')">
                                        <i class="fas fa-eye"></i> 查看详情
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="reReview('${review.report_id}')">
                                        <i class="fas fa-redo"></i> 重新评审
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteReview('${review.id}')">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="review-content">
                            <div class="stats-info">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>合规率:</strong> ${complianceRate}%
                                    </div>
                                    <div class="col-md-3">
                                        <strong>总审查细则:</strong> ${totalCriteria}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>评审时间:</strong> ${new Date(review.created_at).toLocaleString()}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 开始评审
        async function startReview() {
            const reportId = document.getElementById('selectReport').value;

            if (!reportId) {
                alert('请选择要评审的报告');
                return;
            }

            document.querySelector('.loading').style.display = 'block';

            try {
                const response = await fetch(`/api/reviews/${reportId}`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    alert('评审完成');
                    loadReviews();
                    document.getElementById('selectReport').value = '';
                } else {
                    alert('评审失败: ' + result.error);
                }
            } catch (error) {
                alert('评审失败: ' + error.message);
            } finally {
                document.querySelector('.loading').style.display = 'none';
            }
        }

        // 重新评审
        function reReview(reportId) {
            if (confirm('确定要重新评审这个报告吗？')) {
                document.getElementById('selectReport').value = reportId;
                startReview();
            }
        }

        // 查看评审详情
        async function viewReviewDetail(reviewId) {
            try {
                const response = await fetch(`/api/reviews/${reviewId}`);
                const result = await response.json();

                if (result.success) {
                    const review = result.data;
                    const report = allReports.find(r => r.id === review.report_id);
                    const topic = allTopics.find(t => t.id === review.topic_id);

                    displayReviewDetail(review, report, topic);
                    new bootstrap.Modal(document.getElementById('reviewDetailModal')).show();
                } else {
                    alert('获取评审详情失败: ' + result.error);
                }
            } catch (error) {
                alert('获取评审详情失败: ' + error.message);
            }
        }

        // 显示评审详情
        function displayReviewDetail(review, report, topic) {
            const container = document.getElementById('reviewDetailContent');
            const result = review.result;
            const stats = result.statistics || {};

            let content = `
                <div class="mb-4">
                    <h6>基本信息</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>报告名称:</strong> ${report ? report.name : '未知报告'}</p>
                            <p><strong>所属专题:</strong> ${topic ? topic.name : '未分类'}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>评审时间:</strong> ${new Date(review.created_at).toLocaleString()}</p>
                            <p><strong>评审状态:</strong> ${review.status}</p>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h6>统计信息</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-primary">${stats.total_criteria || 0}</h5>
                                <small>总审查细则</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-success">${(stats.result_distribution?.['符合'] || 0) + (stats.result_distribution?.['基本符合'] || 0)}</h5>
                                <small>符合项</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-danger">${stats.result_distribution?.['不符合'] || 0}</h5>
                                <small>不符合项</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-info">${stats.compliance_rate || 0}%</h5>
                                <small>合规率</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            if (result.summary) {
                content += `
                    <div class="mb-4">
                        <h6>总体评审意见</h6>
                        <div class="p-3 bg-light rounded">
                            ${typeof result.summary === 'string' ? result.summary.replace(/\n/g, '<br>') : JSON.stringify(result.summary)}
                        </div>
                    </div>
                `;
            }

            container.innerHTML = content;
        }



        // 切换报告选择状态
        function toggleReportSelection(reportId) {
            if (selectedReports.has(reportId)) {
                selectedReports.delete(reportId);
            } else {
                selectedReports.add(reportId);
            }
            updateSelectedCount();
            displayReviews(allReviews); // 重新渲染以更新选择状态
        }

        // 更新选中数量显示
        function updateSelectedCount() {
            const countElement = document.getElementById('selectedCount');
            if (countElement) {
                countElement.textContent = `已选择 ${selectedReports.size} 个报告`;
            }
        }

        // 全选报告
        function selectAllReports() {
            // 获取当前显示的评审记录对应的报告ID
            const currentReviews = document.querySelectorAll('.review-card input[type="checkbox"]');
            currentReviews.forEach(checkbox => {
                const reportId = checkbox.getAttribute('onchange').match(/'([^']+)'/)[1];
                selectedReports.add(reportId);
            });
            displayReviews(allReviews); // 重新渲染以更新选择状态
        }

        // 清空选择
        function clearAllReports() {
            selectedReports.clear();
            displayReviews(allReviews); // 重新渲染以更新选择状态
        }

        // 生成多报告汇总分析
        async function generateMultiReportSummary() {
            if (selectedReports.size === 0) {
                alert('请至少选择一个报告进行汇总分析');
                return;
            }

            if (!confirm(`确定要对选中的 ${selectedReports.size} 个报告进行汇总分析吗？`)) {
                return;
            }

            document.querySelector('.loading').style.display = 'block';

            try {
                const response = await fetch('/api/multi-report-summary', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        report_ids: Array.from(selectedReports)
                    })
                });
                const result = await response.json();

                if (result.success) {
                    alert('汇总分析完成！');
                    // 可以在这里添加显示汇总结果的逻辑
                    console.log('汇总分析结果:', result.data);

                    // 清空选择
                    clearAllReports();
                } else {
                    alert('汇总分析失败: ' + result.error);
                }
            } catch (error) {
                alert('汇总分析失败: ' + error.message);
            } finally {
                document.querySelector('.loading').style.display = 'none';
            }
        }

        // 批量评审
        async function startBatchReview() {
            if (selectedReports.size === 0) {
                alert('请至少选择一个报告进行评审');
                return;
            }

            if (!confirm(`确定要对选中的 ${selectedReports.size} 个报告进行批量评审吗？`)) {
                return;
            }

            document.querySelector('.loading').style.display = 'block';

            let successCount = 0;
            let failCount = 0;
            const totalCount = selectedReports.size;

            for (const reportId of selectedReports) {
                try {
                    const response = await fetch(`/api/reviews/${reportId}`, {
                        method: 'POST'
                    });
                    const result = await response.json();

                    if (result.success) {
                        successCount++;
                    } else {
                        failCount++;
                        console.error(`评审报告 ${reportId} 失败:`, result.error);
                    }
                } catch (error) {
                    failCount++;
                    console.error(`评审报告 ${reportId} 失败:`, error.message);
                }
            }

            document.querySelector('.loading').style.display = 'none';

            alert(`批量评审完成！\n成功: ${successCount} 个\n失败: ${failCount} 个\n总计: ${totalCount} 个`);

            // 清空选择并刷新列表
            clearAllReports();
            loadReviews();
        }

        // 删除评审记录
        async function deleteReview(reviewId) {
            if (!confirm('确定要删除这条评审记录吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch(`/api/reviews/${reviewId}`, {
                    method: 'DELETE'
                });
                const result = await response.json();

                if (result.success) {
                    alert('评审记录删除成功');
                    loadReviews();
                } else {
                    alert('删除评审记录失败: ' + result.error);
                }
            } catch (error) {
                alert('删除评审记录失败: ' + error.message);
            }
        }
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
